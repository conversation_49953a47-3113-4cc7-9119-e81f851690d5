# Image Mask Components Documentation

## Overview

The Image Mask Components provide a comprehensive solution for applying CSS mask effects to images in React applications. These components support various mask types, animations, and complex layering effects while maintaining accessibility and performance.

## Components

### ImageMask

The main component for applying single mask effects to images.

```tsx
import { ImageMask } from '@/components/ui/image-mask';

<ImageMask
  src="/path/to/image.jpg"
  alt="Masked image"
  mask={{
    type: 'circle',
    radius: '50%',
    centerX: '50%',
    centerY: '50%'
  }}
  animation={{
    enabled: true,
    duration: '2s',
    timing: 'ease-in-out',
    iteration: 'infinite'
  }}
  width={300}
  height={300}
/>
```

### MultiImageMask

Component for applying multiple mask layers with composite effects.

```tsx
import { MultiImageMask } from '@/components/ui/multi-image-mask';

<MultiImageMask
  src="/path/to/image.jpg"
  alt="Complex masked image"
  masks={{
    masks: [
      { type: 'circle', radius: '40%' },
      { type: 'gradient', direction: 'radial' }
    ],
    composite: 'intersect'
  }}
  width={300}
  height={300}
/>
```

## Mask Types

### 1. Circle Masks
- **Type**: `'circle'`
- **Properties**: `radius`, `centerX`, `centerY`
- **Use Cases**: Profile pictures, spotlight effects, circular cutouts

### 2. Ellipse Masks
- **Type**: `'ellipse'`
- **Properties**: `radiusX`, `radiusY`, `centerX`, `centerY`
- **Use Cases**: Portrait frames, oval highlights

### 3. Polygon Masks
- **Type**: `'polygon'`
- **Properties**: `shape` (triangle, diamond, hexagon, octagon, star, arrow), `points`
- **Use Cases**: Geometric designs, modern layouts

### 4. Gradient Masks
- **Type**: `'gradient'`
- **Properties**: `direction`, `stops`, `centerX`, `centerY`
- **Use Cases**: Fade effects, vignettes, smooth transitions

### 5. Pattern Masks
- **Type**: `'pattern'`
- **Properties**: `patternType`, `patternSize`, `patternColor`, `backgroundColor`
- **Use Cases**: Textured overlays, decorative effects

### 6. SVG Masks
- **Type**: `'svg'`
- **Properties**: `svgPath`, `svgContent`, `viewBox`, `maskUrl`
- **Use Cases**: Custom shapes, complex designs

### 7. Custom Masks
- **Type**: `'custom'`
- **Properties**: `maskImage`, `maskUrl`
- **Use Cases**: External mask images, custom designs

## Animation Support

### Animation Configuration
```tsx
animation={{
  enabled: true,
  duration: '2s',
  timing: 'ease-in-out',
  iteration: 'infinite',
  direction: 'alternate',
  fillMode: 'both'
}}
```

### Preset Animations
- **fadeIn**: Single fade-in effect
- **pulse**: Pulsing scale animation
- **rotate**: Continuous rotation
- **bounce**: Bouncing effect

## Preset Effects

### Geometric Presets
- Perfect Circle
- Small Circle
- Oval Portrait
- Wide Oval
- Triangle
- Diamond
- Hexagon
- Star

### Gradient Presets
- Fade to Right
- Fade to Bottom
- Center Spotlight
- Vignette Effect
- Diagonal Fade

### Pattern Presets
- Polka Dots
- Diagonal Stripes
- Checkerboard
- Grid Lines

### Complex Presets
- Double Circle
- Circle with Gradient
- Hexagon Spotlight

## Usage Examples

### Basic Circle Mask
```tsx
<ImageMask
  src="/image.jpg"
  alt="Circular image"
  mask={{ type: 'circle', radius: '50%' }}
  width={200}
  height={200}
/>
```

### Gradient Fade Effect
```tsx
<ImageMask
  src="/image.jpg"
  alt="Faded image"
  mask={{
    type: 'gradient',
    direction: 'to-right',
    stops: [
      { color: 'black', position: '0%' },
      { color: 'transparent', position: '100%' }
    ]
  }}
  width={300}
  height={200}
/>
```

### Complex Multi-Layer Mask
```tsx
<MultiImageMask
  src="/image.jpg"
  alt="Complex masked image"
  masks={{
    masks: [
      { type: 'polygon', shape: 'hexagon' },
      { type: 'gradient', direction: 'radial' }
    ],
    composite: 'intersect'
  }}
  animation={{
    enabled: true,
    duration: '3s',
    timing: 'ease-in-out',
    iteration: 'infinite'
  }}
  width={300}
  height={300}
/>
```

## Accessibility Features

- Proper alt text support
- Focus states for keyboard navigation
- Reduced motion support via `prefers-reduced-motion`
- High contrast mode compatibility
- Screen reader friendly

## Performance Considerations

- Lazy loading support
- Fallback image handling
- Optimized CSS mask generation
- Minimal re-renders with React.memo
- Efficient animation handling

## Browser Support

- Modern browsers with CSS mask support
- Webkit prefix for Safari compatibility
- Graceful degradation for unsupported browsers
- Progressive enhancement approach

## Styling

The components include comprehensive CSS styling:
- Loading states with spinners
- Error state handling
- Responsive design support
- Dark mode compatibility
- Print-friendly styles

## Integration

### CSS Import
Add to your layout or main CSS file:
```css
@import '/public/assets/css/image-mask.css';
```

### TypeScript Support
Full TypeScript support with comprehensive type definitions for all mask configurations and component props.

## Best Practices

1. **Performance**: Use appropriate image sizes and formats
2. **Accessibility**: Always provide meaningful alt text
3. **Responsive**: Consider different screen sizes
4. **Animation**: Use animations sparingly for better UX
5. **Fallbacks**: Provide fallback images for error states
6. **Testing**: Test across different browsers and devices
