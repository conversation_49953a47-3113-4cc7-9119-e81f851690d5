@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
  --font-sans: Inter, sans-serif;
  --font-mono: JetBrains Mono, monospace;
  --font-serif: Laila, ui-serif, serif;
  --radius: 0.375rem;
  --tracking-tighter: calc(var(--tracking-normal) - 0.05em);
  --tracking-tight: calc(var(--tracking-normal) - 0.025em);
  --tracking-wide: calc(var(--tracking-normal) + 0.025em);
  --tracking-wider: calc(var(--tracking-normal) + 0.05em);
  --tracking-widest: calc(var(--tracking-normal) + 0.1em);
  --tracking-normal: var(--tracking-normal);
  --shadow-2xl: var(--shadow-2xl);
  --shadow-xl: var(--shadow-xl);
  --shadow-lg: var(--shadow-lg);
  --shadow-md: var(--shadow-md);
  --shadow: var(--shadow);
  --shadow-sm: var(--shadow-sm);
  --shadow-xs: var(--shadow-xs);
  --shadow-2xs: var(--shadow-2xs);
  --spacing: var(--spacing);
  --letter-spacing: var(--letter-spacing);
  --shadow-offset-y: var(--shadow-offset-y);
  --shadow-offset-x: var(--shadow-offset-x);
  --shadow-spread: var(--shadow-spread);
  --shadow-blur: var(--shadow-blur);
  --shadow-opacity: var(--shadow-opacity);
  --color-shadow-color: var(--shadow-color);
  --color-destructive-foreground: var(--destructive-foreground);
}

:root {
  --background: oklch(0.9842 0.0034 247.8575);
  --foreground: oklch(0.2897 0.1014 256.6486);
  --card: oklch(1.0000 0 0);
  --card-foreground: oklch(0.2897 0.1014 256.6486);
  --popover: oklch(1.0000 0 0);
  --popover-foreground: oklch(0.2897 0.1014 256.6486);
  --primary: oklch(0.7602 0.1482 68.0284);
  --primary-foreground: oklch(1.0000 0 0);
  --secondary: oklch(0.9521 0.0229 258.3593);
  --secondary-foreground: oklch(0.2897 0.1014 256.6486);
  --muted: oklch(0.9670 0.0029 264.5419);
  --muted-foreground: oklch(0.5510 0.0234 264.3637);
  --accent: oklch(0.9739 0.0225 78.2215);
  --accent-foreground: oklch(0.3898 0.0695 71.3261);
  --destructive: oklch(0.6368 0.2078 25.3313);
  --destructive-foreground: oklch(1.0000 0 0);
  --border: oklch(0.8717 0.0093 258.3382);
  --input: oklch(0.8717 0.0093 258.3382);
  --ring: oklch(0.7602 0.1482 68.0284);
  --chart-1: oklch(0.2897 0.1014 256.6486);
  --chart-2: oklch(0.3638 0.1331 257.7295);
  --chart-3: oklch(0.4374 0.1631 258.1664);
  --chart-4: oklch(0.5051 0.1917 258.5560);
  --chart-5: oklch(0.5706 0.2193 258.8213);
  --sidebar: oklch(0.2897 0.1014 256.6486);
  --sidebar-foreground: oklch(1.0000 0 0);
  --sidebar-primary: oklch(0.7602 0.1482 68.0284);
  --sidebar-primary-foreground: oklch(0.2897 0.1014 256.6486);
  --sidebar-accent: oklch(0.3638 0.1331 257.7295);
  --sidebar-accent-foreground: oklch(1.0000 0 0);
  --sidebar-border: oklch(0.3638 0.1331 257.7295);
  --sidebar-ring: oklch(0.7602 0.1482 68.0284);
  --font-sans: Inter, sans-serif;
  --font-serif: Laila, ui-serif, serif;
  --font-mono: JetBrains Mono, monospace;
  --radius: 0.375rem;
  --shadow-2xs: 0px 4px 8px -1px hsl(0 0% 0% / 0.05);
  --shadow-xs: 0px 4px 8px -1px hsl(0 0% 0% / 0.05);
  --shadow-sm: 0px 4px 8px -1px hsl(0 0% 0% / 0.10), 0px 1px 2px -2px hsl(0 0% 0% / 0.10);
  --shadow: 0px 4px 8px -1px hsl(0 0% 0% / 0.10), 0px 1px 2px -2px hsl(0 0% 0% / 0.10);
  --shadow-md: 0px 4px 8px -1px hsl(0 0% 0% / 0.10), 0px 2px 4px -2px hsl(0 0% 0% / 0.10);
  --shadow-lg: 0px 4px 8px -1px hsl(0 0% 0% / 0.10), 0px 4px 6px -2px hsl(0 0% 0% / 0.10);
  --shadow-xl: 0px 4px 8px -1px hsl(0 0% 0% / 0.10), 0px 8px 10px -2px hsl(0 0% 0% / 0.10);
  --shadow-2xl: 0px 4px 8px -1px hsl(0 0% 0% / 0.25);
  --tracking-normal: 0em;
  --spacing: 0.22rem;
  --shadow-color: #000000;
  --shadow-opacity: 0.1;
  --shadow-blur: 8px;
  --shadow-spread: -1px;
  --shadow-offset-x: 0px;
  --shadow-offset-y: 4px;
  --letter-spacing: 0em;
}

.dark {
  --background: oklch(0.2181 0.0673 253.0456);
  --foreground: oklch(0.9684 0.0108 256.6948);
  --card: oklch(0.2897 0.1014 256.6486);
  --card-foreground: oklch(0.9684 0.0108 256.6948);
  --popover: oklch(0.2897 0.1014 256.6486);
  --popover-foreground: oklch(0.9684 0.0108 256.6948);
  --primary: oklch(0.7602 0.1482 68.0284);
  --primary-foreground: oklch(0.2897 0.1014 256.6486);
  --secondary: oklch(0.3543 0.0906 257.6220);
  --secondary-foreground: oklch(0.9057 0.0296 257.7675);
  --muted: oklch(0.2897 0.1014 256.6486);
  --muted-foreground: oklch(0.7805 0.0391 256.9841);
  --accent: oklch(0.3964 0.0797 257.6538);
  --accent-foreground: oklch(0.9057 0.0296 257.7675);
  --destructive: oklch(0.6368 0.2078 25.3313);
  --destructive-foreground: oklch(0.2181 0.0673 253.0456);
  --border: oklch(0.3543 0.0906 257.6220);
  --input: oklch(0.3543 0.0906 257.6220);
  --ring: oklch(0.7602 0.1482 68.0284);
  --chart-1: oklch(0.7602 0.1482 68.0284);
  --chart-2: oklch(0.7023 0.1366 68.1559);
  --chart-3: oklch(0.6432 0.1242 68.1030);
  --chart-4: oklch(0.5846 0.1119 69.1595);
  --chart-5: oklch(0.5238 0.0996 68.6089);
  --sidebar: oklch(0.2897 0.1014 256.6486);
  --sidebar-foreground: oklch(0.9684 0.0108 256.6948);
  --sidebar-primary: oklch(0.7602 0.1482 68.0284);
  --sidebar-primary-foreground: oklch(0.2897 0.1014 256.6486);
  --sidebar-accent: oklch(0.3964 0.0797 257.6538);
  --sidebar-accent-foreground: oklch(0.9057 0.0296 257.7675);
  --sidebar-border: oklch(0.3543 0.0906 257.6220);
  --sidebar-ring: oklch(0.7602 0.1482 68.0284);
  --font-sans: Inter, sans-serif;
  --font-serif: Laila, ui-serif, serif;
  --font-mono: JetBrains Mono, monospace;
  --radius: 0.375rem;
  --shadow-2xs: 0px 4px 8px -1px hsl(0 0% 0% / 0.05);
  --shadow-xs: 0px 4px 8px -1px hsl(0 0% 0% / 0.05);
  --shadow-sm: 0px 4px 8px -1px hsl(0 0% 0% / 0.10), 0px 1px 2px -2px hsl(0 0% 0% / 0.10);
  --shadow: 0px 4px 8px -1px hsl(0 0% 0% / 0.10), 0px 1px 2px -2px hsl(0 0% 0% / 0.10);
  --shadow-md: 0px 4px 8px -1px hsl(0 0% 0% / 0.10), 0px 2px 4px -2px hsl(0 0% 0% / 0.10);
  --shadow-lg: 0px 4px 8px -1px hsl(0 0% 0% / 0.10), 0px 4px 6px -2px hsl(0 0% 0% / 0.10);
  --shadow-xl: 0px 4px 8px -1px hsl(0 0% 0% / 0.10), 0px 8px 10px -2px hsl(0 0% 0% / 0.10);
  --shadow-2xl: 0px 4px 8px -1px hsl(0 0% 0% / 0.25);
  --shadow-color: #000000;
  --shadow-opacity: 0.1;
  --shadow-blur: 8px;
  --shadow-spread: -1px;
  --shadow-offset-x: 0px;
  --shadow-offset-y: 4px;
  --letter-spacing: 0em;
  --spacing: 0.22rem;
}

body {
  letter-spacing: var(--tracking-normal);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
    letter-spacing: var(--tracking-normal);
  }
}