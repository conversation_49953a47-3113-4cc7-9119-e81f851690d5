'use client'

import { BlogAdminTest } from '@/components/admin-blog/blog-admin-test'
import { But<PERSON> } from '@/components/ui/button'
import { ArrowLeft } from 'lucide-react'
import Link from 'next/link'

export default function BlogTestPage() {
  return (
    <div className="container mx-auto py-6">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Blog Administration Test</h1>
            <p className="text-muted-foreground">
              Test the blog post administration functionality
            </p>
          </div>
          <Button variant="outline" asChild>
            <Link href="/admin/blog">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Blog Admin
            </Link>
          </Button>
        </div>

        {/* Test Component */}
        <BlogAdminTest />
      </div>
    </div>
  )
}
