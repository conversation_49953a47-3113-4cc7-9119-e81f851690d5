import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/auth'
import { uploadImage, getImagePreview } from '@/utils/appwrite'

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await auth()
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Authentication required' 
        },
        { status: 401 }
      )
    }

    const formData = await request.formData()
    const file = formData.get('file') as File
    
    if (!file) {
      return NextResponse.json(
        { 
          success: false,
          error: 'No file provided' 
        },
        { status: 400 }
      )
    }

    // Validate file type
    if (!file.type.startsWith('image/')) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Only image files are allowed' 
        },
        { status: 400 }
      )
    }

    // Validate file size (max 5MB)
    const maxSize = 5 * 1024 * 1024 // 5MB
    if (file.size > maxSize) {
      return NextResponse.json(
        { 
          success: false,
          error: 'File size must be less than 5MB' 
        },
        { status: 400 }
      )
    }

    // Upload to Appwrite storage
    const fileId = await uploadImage(file)
    const imageUrl = getImagePreview(fileId).toString()

    return NextResponse.json({
      success: true,
      data: {
        fileId,
        url: imageUrl,
        filename: file.name,
        size: file.size,
        type: file.type
      }
    })

  } catch (error) {
    console.error('Image upload API error:', error)
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to upload image' 
      },
      { status: 500 }
    )
  }
}
