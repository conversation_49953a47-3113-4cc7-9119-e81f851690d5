'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { ImageUpload } from '@/components/ui/image-upload'
import { Editor } from '@/components/editor/editor'
import { useToast } from '@/hooks/use-toast'
import { CheckCircle, XCircle, AlertCircle } from 'lucide-react'

interface TestResult {
  name: string
  status: 'pending' | 'success' | 'error'
  message?: string
}

export function BlogAdminTest() {
  const [imageUrl, setImageUrl] = useState('')
  const [editorContent, setEditorContent] = useState('')
  const [testResults, setTestResults] = useState<TestResult[]>([
    { name: 'Image Upload', status: 'pending' },
    { name: 'Rich Text Editor', status: 'pending' },
    { name: 'Content Persistence', status: 'pending' },
    { name: 'Frontend Display', status: 'pending' }
  ])
  const { toast } = useToast()

  const updateTestResult = (testName: string, status: 'success' | 'error', message?: string) => {
    setTestResults(prev => prev.map(test => 
      test.name === testName ? { ...test, status, message } : test
    ))
  }

  const testImageUpload = async () => {
    try {
      // Test will be triggered by actually uploading an image
      if (imageUrl) {
        updateTestResult('Image Upload', 'success', 'Image uploaded successfully')
        toast({
          title: "Image Upload Test",
          description: "✅ Image upload functionality is working correctly",
        })
      } else {
        updateTestResult('Image Upload', 'error', 'No image uploaded')
      }
    } catch (error) {
      updateTestResult('Image Upload', 'error', 'Image upload failed')
    }
  }

  const testRichTextEditor = () => {
    try {
      if (editorContent && editorContent.length > 10) {
        updateTestResult('Rich Text Editor', 'success', 'Editor content saved successfully')
        toast({
          title: "Rich Text Editor Test",
          description: "✅ Rich text editor is working correctly",
        })
      } else {
        updateTestResult('Rich Text Editor', 'error', 'No content in editor')
      }
    } catch (error) {
      updateTestResult('Rich Text Editor', 'error', 'Editor test failed')
    }
  }

  const testContentPersistence = () => {
    try {
      // Simulate content persistence test
      const hasImage = !!imageUrl
      const hasContent = !!editorContent
      
      if (hasImage && hasContent) {
        updateTestResult('Content Persistence', 'success', 'Content can be persisted')
        toast({
          title: "Content Persistence Test",
          description: "✅ Content persistence is working correctly",
        })
      } else {
        updateTestResult('Content Persistence', 'error', 'Missing image or content')
      }
    } catch (error) {
      updateTestResult('Content Persistence', 'error', 'Persistence test failed')
    }
  }

  const testFrontendDisplay = () => {
    try {
      // Simulate frontend display test
      const canDisplayImage = !!imageUrl && (imageUrl.startsWith('http') || imageUrl.startsWith('/'))
      const canDisplayContent = !!editorContent && editorContent.includes('<')
      
      if (canDisplayImage && canDisplayContent) {
        updateTestResult('Frontend Display', 'success', 'Content can be displayed on frontend')
        toast({
          title: "Frontend Display Test",
          description: "✅ Frontend display is working correctly",
        })
      } else {
        updateTestResult('Frontend Display', 'error', 'Content cannot be displayed properly')
      }
    } catch (error) {
      updateTestResult('Frontend Display', 'error', 'Display test failed')
    }
  }

  const runAllTests = () => {
    testImageUpload()
    testRichTextEditor()
    testContentPersistence()
    testFrontendDisplay()
  }

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-5 w-5 text-green-500" />
      case 'error':
        return <XCircle className="h-5 w-5 text-red-500" />
      default:
        return <AlertCircle className="h-5 w-5 text-yellow-500" />
    }
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Blog Administration Test Suite</CardTitle>
          <CardDescription>
            Test the blog post creation functionality including image upload and rich text editing
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Image Upload Test */}
          <div className="space-y-2">
            <h3 className="text-lg font-medium">1. Featured Image Upload</h3>
            <ImageUpload
              value={imageUrl}
              onChange={(url) => {
                setImageUrl(url)
                if (url) {
                  updateTestResult('Image Upload', 'success', 'Image uploaded successfully')
                }
              }}
              placeholder="Upload a test image"
              maxSize={5}
            />
          </div>

          {/* Rich Text Editor Test */}
          <div className="space-y-2">
            <h3 className="text-lg font-medium">2. Rich Text Editor</h3>
            <Editor
              content={editorContent}
              onChange={(content) => {
                setEditorContent(content)
                if (content && content.length > 10) {
                  updateTestResult('Rich Text Editor', 'success', 'Editor content updated')
                }
              }}
              placeholder="Type some content to test the rich text editor..."
              className="min-h-[200px]"
            />
          </div>

          {/* Test Results */}
          <div className="space-y-2">
            <h3 className="text-lg font-medium">Test Results</h3>
            <div className="space-y-2">
              {testResults.map((test) => (
                <div key={test.name} className="flex items-center justify-between p-3 border rounded-md">
                  <div className="flex items-center space-x-3">
                    {getStatusIcon(test.status)}
                    <span className="font-medium">{test.name}</span>
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {test.message || test.status}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Test Actions */}
          <div className="flex space-x-2">
            <Button onClick={runAllTests}>
              Run All Tests
            </Button>
            <Button variant="outline" onClick={testImageUpload}>
              Test Image Upload
            </Button>
            <Button variant="outline" onClick={testRichTextEditor}>
              Test Rich Text Editor
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
