'use client'

import { zod<PERSON>esolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { But<PERSON> } from "@/components/ui/button"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { useToast } from "@/hooks/use-toast"
import { useCallback, useState } from "react"
import { uploadImage, getImagePreview } from "@/utils/appwrite"
import { Loader2, UploadCloud, X } from "lucide-react"
import { Editor } from "@/components/editor/editor"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem } from "@/components/ui/command"
import { Pop<PERSON>, <PERSON>over<PERSON>ontent, PopoverTrigger } from "@/components/ui/popover"
import { Check, ChevronsUpDown } from "lucide-react"
import { cn } from "@/lib/utils"



const formSchema = z.object({
  title: z.string().min(2, {
    message: "Title must be at least 2 characters.",
  }),
  slug: z.string().min(2, {
    message: "Slug must be at least 2 characters.",
  }),
  excerpt: z.string().min(10, {
    message: "Excerpt must be at least 10 characters.",
  }),
  content: z.string().min(50, {
    message: "Content must be at least 50 characters.",
  }),
  featuredImage: z.string().url({
    message: "Please upload a valid image.",
  }),
  published: z.boolean().default(false),
  categories: z.array(z.string()).default([]),
  metaTitle: z.string().optional(),
  metaDescription: z.string().optional(),
  tags: z.array(z.string()).default([]),
  readTime: z.number().min(1, {
    message: "Read time must be at least 1 minute.",
  }),
})

type BlogFormValues = z.infer<typeof formSchema>

interface BlogFormProps {
  initialData?: Partial<BlogFormValues> & { id?: string }
  onSuccess?: () => void
}

const categories = [
  { label: "Technology", value: "technology" },
  { label: "Programming", value: "programming" },
  { label: "Web Development", value: "web-dev" },
  { label: "Mobile Development", value: "mobile-dev" },
  { label: "UI/UX", value: "ui-ux" },
  { label: "DevOps", value: "devops" },
  { label: "Cloud Computing", value: "cloud" },
  { label: "AI/ML", value: "ai-ml" },
] as const

export function BlogForm({ initialData, onSuccess }: BlogFormProps = {}) {
  const { toast } = useToast()
  const [isUploading, setIsUploading] = useState(false)
  const [previewUrl, setPreviewUrl] = useState<string | null>(null)
  const [open, setOpen] = useState(false)
  
  const handleImageUpload = async (file: File) => {
    setIsUploading(true)
    try {
      const fileId = await uploadImage(file)
      const imageUrl = getImagePreview(fileId).toString()

      form.setValue("featuredImage", imageUrl)
      setPreviewUrl(imageUrl)

      toast({
        title: "Success!",
        description: "Image uploaded successfully.",
      })
    } catch (error) {
      console.error('Error uploading image:', error)
      toast({
        title: "Error",
        description: "Failed to upload image. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsUploading(false)
    }
  }

  const onDrop = useCallback((acceptedFiles: File[]) => {
    if (acceptedFiles[0]) {
      handleImageUpload(acceptedFiles[0])
    }
  }, [])

  const fileTypes = ["image/png", "image/jpeg", "image/jpg", "image/webp"]

  const { getRootProps, getInputProps } = useDropzone({
    onDrop,
    accept: {
      'image/*': ['.png', '.jpg', '.jpeg', '.webp']
    },
  })

  const form = useForm<BlogFormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      title: initialData?.title || "",
      slug: initialData?.slug || "",
      excerpt: initialData?.excerpt || "",
      content: initialData?.content || "",
      featuredImage: initialData?.featuredImage || "",
      published: initialData?.published || false,
      categories: initialData?.categories || [],
      metaTitle: initialData?.metaTitle || "",
      metaDescription: initialData?.metaDescription || "",
      tags: initialData?.tags || [],
      readTime: initialData?.readTime || 5,
    },
  })

  const generateSlug = (title: string) => {
    return title
      .toLowerCase()
      .replace(/[^\w\s-]/g, '') // Remove special characters
      .replace(/\s+/g, '-') // Replace spaces with hyphens
      .replace(/--+/g, '-') // Replace multiple hyphens with single hyphen
      .trim()
  }

  const handleTitleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { formState: { errors }, setValue, watch, control } = form as any // Temporarily using any to bypass type issues as any
    const title = e.target.value
    setValue('title', title)
    
    // Auto-generate slug if it's empty or hasn't been manually modified
    if (!form.getValues('slug') || form.getValues('slug') === form.formState.defaultValues?.slug) {
      setValue('slug', generateSlug(title))
    }
    
    // Auto-generate meta title if empty
    if (!form.getValues('metaTitle')) {
      form.setValue('metaTitle', title)
    }
    
    // Auto-generate meta description if empty
    if (!form.getValues('metaDescription') && form.getValues('excerpt')) {
      form.setValue('metaDescription', form.getValues('excerpt'))
    }
  }

  const calculateReadTime = (content: string) => {
    // Average reading speed: 200 words per minute
    const wordsPerMinute = 200
    const words = content.trim().split(/\s+/).length
    return Math.ceil(words / wordsPerMinute) || 1
  }

  const handleContentChange = (content: string) => {
    form.setValue('content', content)
    // Update read time when content changes
    form.setValue('readTime', calculateReadTime(content))
  }

  async function onSubmit(data: BlogFormValues) {
    try {
      // Here you would typically send the data to your API
      console.log('Submitting form data:', data)
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // Show success toast
      toast({
        title: "Success!",
        description: initialData?.id 
          ? "Your blog post has been updated successfully."
          : "Your blog post has been created successfully.",
      })
      
      // Call onSuccess callback if provided
      if (onSuccess) {
        onSuccess()
      }
    } catch (error) {
      console.error('Error submitting form:', error)
      toast({
        title: "Error",
        description: "There was an error saving your post. Please try again.",
        variant: "destructive",
      })
    }
  }

  const removeImage = () => {
    form.setValue("featuredImage", "")
    setPreviewUrl(null)
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        <div className="grid grid-cols-1 gap-8 md:grid-cols-3">
          <div className="space-y-8 md:col-span-2">
            <FormField
              control={form.control}
              name="title"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Title</FormLabel>
                  <FormControl>
                    <Input 
                      placeholder="Blog post title" 
                      {...field} 
                      onChange={(e) => {
                        field.onChange(e)
                        handleTitleChange(e)
                      }}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="slug"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Slug</FormLabel>
                  <FormControl>
                    <div className="flex space-x-2">
                      <div className="flex items-center px-3 border border-r-0 rounded-l-md bg-muted text-muted-foreground text-sm">
                        /blog/
                      </div>
                      <Input 
                        className="rounded-l-none"
                        placeholder="blog-post-slug" 
                        {...field} 
                      />
                    </div>
                  </FormControl>
                  <FormDescription>
                    This will be used in the URL. Only use lowercase letters, numbers, and hyphens.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="excerpt"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Excerpt</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="A short description of the blog post that will be shown in the blog list and social media previews."
                      className="resize-none min-h-[100px]"
                      {...field}
                      onChange={(e) => {
                        field.onChange(e)
                        // Update meta description if it's still using the default
                        if (!form.getValues('metaDescription') || 
                            form.getValues('metaDescription') === form.formState.defaultValues?.metaDescription) {
                          form.setValue('metaDescription', e.target.value)
                        }
                      }}
                    />
                  </FormControl>
                  <FormDescription>
                    A brief summary of your blog post (max 200 characters).
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="content"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Content</FormLabel>
                  <FormControl>
                    <Editor 
                      content={field.value} 
                      onChange={(content) => {
                        field.onChange(content)
                        handleContentChange(content)
                      }} 
                      placeholder="Start writing your blog post here..."
                      className="blog-content-editor"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="space-y-4">
              <div>
                <FormLabel>Featured Image</FormLabel>
                {previewUrl || form.getValues("featuredImage") ? (
                  <div className="mt-2 relative group">
                    <div className="relative aspect-video rounded-md overflow-hidden border">
                      <img
                        src={previewUrl || form.getValues("featuredImage")}
                        alt="Preview"
                        className="w-full h-full object-cover"
                      />
                      <button
                        type="button"
                        onClick={removeImage}
                        className="absolute top-2 right-2 bg-red-500 text-white p-1.5 rounded-full hover:bg-red-600 transition-colors"
                      >
                        <X className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                ) : (
                  <div
                    {...getRootProps()}
                    className="mt-2 flex justify-center px-6 pt-5 pb-6 border-2 border-dashed border-muted-foreground/25 rounded-md hover:border-primary/50 transition-colors cursor-pointer"
                  >
                    <div className="space-y-1 text-center">
                      <div className="flex justify-center">
                        {isUploadingImage ? (
                          <Loader2 className="h-10 w-10 text-muted-foreground animate-spin" />
                        ) : (
                          <UploadCloud className="h-10 w-10 text-muted-foreground" />
                        )}
                      </div>
                      <div className="flex text-sm text-muted-foreground">
                        <span className="relative cursor-pointer bg-background rounded-md font-medium text-primary hover:text-primary/80 focus-within:outline-none">
                          Upload an image
                        </span>
                        <p className="pl-1">or drag and drop</p>
                      </div>
                      <p className="text-xs text-muted-foreground">
                        PNG, JPG, GIF up to 5MB
                      </p>
                    </div>
                    <input {...getInputProps()} className="hidden" />
                  </div>
                )}
                <FormMessage />
              </div>
            </div>
          </div>

          <div className="space-y-8">
            <div className="rounded-lg border bg-card p-6 space-y-6">
              <h3 className="font-medium">Publish</h3>
              
              <FormField
                control={form.control}
                name="published"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                    <div className="space-y-0.5">
                      <FormLabel>Status</FormLabel>
                      <FormDescription>
                        {field.value ? "This post will be visible to everyone." : "This post will be saved as a draft."}
                      </FormDescription>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />

              <div className="flex justify-end">
                <Button type="submit" disabled={isUploading}>
                  {isUploading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Saving...
                    </>
                  ) : form.getValues("published") ? (
                    "Publish"
                  ) : (
                    "Save as Draft"
                  )}
                </Button>
              </div>
            </div>

            <div className="rounded-lg border bg-card p-6 space-y-6">
              <h3 className="font-medium">Categories</h3>
              <FormField
                control={form.control}
                name="categories"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <Popover open={open} onOpenChange={setOpen}>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant="outline"
                            role="combobox"
                            className={cn(
                              "justify-between",
                              !field.value && "text-muted-foreground"
                            )}
                          >
                            {field.value && field.value.length > 0
                              ? `${field.value.length} selected`
                              : "Select categories"}
                            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-[200px] p-0">
                        <Command>
                          <CommandInput placeholder="Search categories..." />
                          <CommandEmpty>No categories found.</CommandEmpty>
                          <CommandGroup className="max-h-[200px] overflow-auto">
                            {categories.map((category) => (
                              <CommandItem
                                value={category.label}
                                key={category.value}
                                onSelect={() => {
                                  const currentValues = field.value || []
                                  const newValues = currentValues.includes(category.value)
                                    ? currentValues.filter((v) => v !== category.value)
                                    : [...currentValues, category.value]
                                  field.onChange(newValues)
                                }}
                              >
                                <Check
                                  className={cn(
                                    "mr-2 h-4 w-4",
                                    field.value?.includes(category.value)
                                      ? "opacity-100"
                                      : "opacity-0"
                                  )}
                                />
                                {category.label}
                              </CommandItem>
                            ))}
                          </CommandGroup>
                        </Command>
                      </PopoverContent>
                    </Popover>
                    <div className="flex flex-wrap gap-2 mt-2">
                      {field.value?.map((value) => (
                        <Badge key={value} variant="secondary" className="flex items-center gap-1">
                          {categories.find((c) => c.value === value)?.label}
                          <button
                            type="button"
                            onClick={() => {
                              field.onChange(field.value.filter((v) => v !== value))
                            }}
                            className="ml-1 rounded-full hover:bg-muted"
                          >
                            <X className="h-3 w-3" />
                          </button>
                        </Badge>
                      ))}
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="tags"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Tags</FormLabel>
                    <FormControl>
                      <Input 
                        placeholder="Add tags (press Enter to add)" 
                        onKeyDown={(e) => {
                          if (e.key === 'Enter') {
                            e.preventDefault()
                            const value = e.currentTarget.value.trim()
                            if (value && !field.value?.includes(value)) {
                              field.onChange([...(field.value || []), value])
                              e.currentTarget.value = ''
                            }
                          }
                        }}
                      />
                    </FormControl>
                    <div className="flex flex-wrap gap-2 mt-2">
                      {field.value?.map((tag, index) => (
                        <Badge key={index} variant="outline" className="flex items-center gap-1">
                          {tag}
                          <button
                            type="button"
                            onClick={() => {
                              field.onChange(field.value.filter((_, i) => i !== index))
                            }}
                            className="ml-1 rounded-full hover:bg-muted"
                          >
                            <X className="h-3 w-3" />
                          </button>
                        </Badge>
                      ))}
                    </div>
                    <FormDescription>
                      Add tags to help readers find your post.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="rounded-lg border bg-card p-6 space-y-6">
              <h3 className="font-medium">SEO</h3>
              
              <FormField
                control={form.control}
                name="metaTitle"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Meta Title</FormLabel>
                    <FormControl>
                      <Input 
                        placeholder="SEO title for search engines" 
                        {...field} 
                        onChange={(e) => {
                          field.onChange(e)
                        }}
                      />
                    </FormControl>
                    <FormDescription>
                      Recommended: 50-60 characters
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="metaDescription"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Meta Description</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="SEO description for search engines"
                        className="min-h-[100px]"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      Recommended: 150-160 characters
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="rounded-lg border bg-card p-6 space-y-4">
              <h3 className="font-medium">Post Details</h3>
              
              <div className="space-y-2">
                <Label>Read Time</Label>
                <div className="flex items-center space-x-2">
                  <Input 
                    type="number" 
                    min="1" 
                    className="w-20"
                    value={form.watch('readTime')}
                    onChange={(e) => form.setValue('readTime', parseInt(e.target.value) || 1)}
                  />
                  <span className="text-sm text-muted-foreground">
                    {form.watch('readTime') === 1 ? 'minute' : 'minutes'}
                  </span>
                </div>
                <p className="text-xs text-muted-foreground">
                  Estimated reading time for this post.
                </p>
              </div>
            </div>
          </div>
        </div>
      </form>
    </Form>
  )
}
