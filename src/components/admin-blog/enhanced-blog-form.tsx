'use client'

import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { useToast } from "@/hooks/use-toast"
import { useCallback, useState, useEffect } from "react"
import { Loader2, UploadCloud, X, Calendar, Save, Eye, Send } from "lucide-react"
import { Editor } from "@/components/editor/editor"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { generateReactHelpers } from "@uploadthing/react"
import type { OurFileRouter } from "@/app/api/uploadthing/core"
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem } from "@/components/ui/command"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Check, ChevronsUpDown } from "lucide-react"
import { cn } from "@/lib/utils"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Calendar as CalendarComponent } from "@/components/ui/calendar"
import { format } from "date-fns"
import { createPostSchema } from "@/lib/services/blog.service"
import { BlogCategory, BlogTag, CreateBlogPostData, UpdateBlogPostData, BlogPost } from "@/types/blog"
import { PostStatus } from "@prisma/client"

interface EnhancedBlogFormProps {
  initialData?: Partial<BlogPost>
  categories: BlogCategory[]
  tags: BlogTag[]
  onSubmit: (data: CreateBlogPostData | UpdateBlogPostData) => Promise<void>
  onCancel?: () => void
  loading?: boolean
  mode?: 'create' | 'edit'
}

export function EnhancedBlogForm({ 
  initialData, 
  categories, 
  tags, 
  onSubmit, 
  onCancel, 
  loading = false,
  mode = 'create'
}: EnhancedBlogFormProps) {
  const { toast } = useToast()
  const [isUploading, setIsUploading] = useState(false)
  const [previewUrl, setPreviewUrl] = useState<string | null>(initialData?.featuredImage || null)
  const [categoryOpen, setCategoryOpen] = useState(false)
  const [tagInput, setTagInput] = useState('')
  const [selectedTags, setSelectedTags] = useState<string[]>(
    initialData?.tags?.map(tag => tag.id) || []
  )
  const [showScheduler, setShowScheduler] = useState(false)
  const [scheduledDate, setScheduledDate] = useState<Date | undefined>(
    initialData?.scheduledAt ? new Date(initialData.scheduledAt) : undefined
  )

  const form = useForm<CreateBlogPostData>({
    resolver: zodResolver(createPostSchema),
    defaultValues: {
      title: initialData?.title || "",
      slug: initialData?.slug || "",
      excerpt: initialData?.excerpt || "",
      content: initialData?.content || "",
      featuredImage: initialData?.featuredImage || "",
      status: initialData?.status || PostStatus.DRAFT,
      categoryId: initialData?.categoryId || "",
      tags: selectedTags,
      seoTitle: initialData?.seoTitle || "",
      seoDescription: initialData?.seoDescription || "",
      seoKeywords: [],
    },
  })

  // Auto-generate slug from title
  const generateSlug = (title: string) => {
    return title
      .toLowerCase()
      .replace(/[^\w\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/--+/g, '-')
      .trim()
  }

  const handleTitleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const title = e.target.value
    form.setValue('title', title)
    
    // Auto-generate slug if it's empty or hasn't been manually modified
    if (!form.getValues('slug') || mode === 'create') {
      form.setValue('slug', generateSlug(title))
    }
    
    // Auto-generate meta title if empty
    if (!form.getValues('seoTitle')) {
      form.setValue('seoTitle', title)
    }
  }

  const handleExcerptChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const excerpt = e.target.value
    form.setValue('excerpt', excerpt)
    
    // Auto-generate meta description if empty
    if (!form.getValues('seoDescription')) {
      form.setValue('seoDescription', excerpt)
    }
  }

  const calculateReadTime = (content: string) => {
    const wordsPerMinute = 200
    const words = content.trim().split(/\s+/).length
    return Math.ceil(words / wordsPerMinute) || 1
  }

  const handleContentChange = (content: string) => {
    form.setValue('content', content)
  }

  const addTag = (tagName: string) => {
    if (tagName && !selectedTags.includes(tagName)) {
      const newTags = [...selectedTags, tagName]
      setSelectedTags(newTags)
      form.setValue('tags', newTags)
    }
    setTagInput('')
  }

  const removeTag = (tagToRemove: string) => {
    const newTags = selectedTags.filter(tag => tag !== tagToRemove)
    setSelectedTags(newTags)
    form.setValue('tags', newTags)
  }

  const handleSubmit = async (data: CreateBlogPostData) => {
    try {
      const submitData = {
        ...data,
        tags: selectedTags,
        scheduledAt: showScheduler && scheduledDate ? scheduledDate.toISOString() : null,
        ...(mode === 'edit' && initialData?.id ? { id: initialData.id } : {})
      }

      await onSubmit(submitData as any)
      
      toast({
        title: "Success!",
        description: mode === 'edit' 
          ? "Blog post updated successfully."
          : "Blog post created successfully.",
      })
    } catch (error) {
      console.error('Error submitting form:', error)
      toast({
        title: "Error",
        description: "There was an error saving your post. Please try again.",
        variant: "destructive",
      })
    }
  }

  const handleSaveDraft = () => {
    form.setValue('status', PostStatus.DRAFT)
    form.handleSubmit(handleSubmit)()
  }

  const handlePublish = () => {
    form.setValue('status', PostStatus.PUBLISHED)
    form.setValue('publishedAt', new Date().toISOString())
    form.handleSubmit(handleSubmit)()
  }

  const handleSchedule = () => {
    if (!scheduledDate) {
      toast({
        title: "Error",
        description: "Please select a date and time to schedule the post.",
        variant: "destructive",
      })
      return
    }
    
    form.setValue('status', PostStatus.SCHEDULED)
    form.setValue('scheduledAt', scheduledDate.toISOString())
    form.handleSubmit(handleSubmit)()
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-8">
        <div className="grid grid-cols-1 gap-8 lg:grid-cols-3">
          {/* Main Content Column */}
          <div className="space-y-8 lg:col-span-2">
            <FormField
              control={form.control}
              name="title"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Title</FormLabel>
                  <FormControl>
                    <Input 
                      placeholder="Enter your blog post title" 
                      {...field} 
                      onChange={(e) => {
                        field.onChange(e)
                        handleTitleChange(e)
                      }}
                      className="text-lg font-medium"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="slug"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>URL Slug</FormLabel>
                  <FormControl>
                    <div className="flex space-x-2">
                      <div className="flex items-center px-3 border border-r-0 rounded-l-md bg-muted text-muted-foreground text-sm">
                        /blog/
                      </div>
                      <Input 
                        className="rounded-l-none"
                        placeholder="url-friendly-slug" 
                        {...field} 
                      />
                    </div>
                  </FormControl>
                  <FormDescription>
                    This will be used in the URL. Only lowercase letters, numbers, and hyphens.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="excerpt"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Excerpt</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="A compelling summary that will appear in blog listings and social media previews..."
                      className="resize-none min-h-[120px]"
                      {...field}
                      onChange={(e) => {
                        field.onChange(e)
                        handleExcerptChange(e)
                      }}
                    />
                  </FormControl>
                  <FormDescription>
                    A brief summary of your blog post (recommended: 150-200 characters).
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="content"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Content</FormLabel>
                  <FormControl>
                    <Editor 
                      content={field.value} 
                      onChange={(content) => {
                        field.onChange(content)
                        handleContentChange(content)
                      }} 
                      placeholder="Start writing your blog post here..."
                      className="min-h-[400px]"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          {/* Sidebar Column */}
          <div className="space-y-6">
            {/* Publishing Controls */}
            <div className="rounded-lg border bg-card p-6 space-y-4">
              <h3 className="font-semibold flex items-center gap-2">
                <Send className="h-4 w-4" />
                Publish
              </h3>
              
              <div className="space-y-3">
                <Button 
                  type="button" 
                  variant="outline" 
                  onClick={handleSaveDraft}
                  disabled={loading}
                  className="w-full"
                >
                  <Save className="mr-2 h-4 w-4" />
                  Save as Draft
                </Button>
                
                <Button 
                  type="button" 
                  onClick={handlePublish}
                  disabled={loading}
                  className="w-full"
                >
                  <Eye className="mr-2 h-4 w-4" />
                  Publish Now
                </Button>
                
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="schedule"
                      checked={showScheduler}
                      onCheckedChange={setShowScheduler}
                    />
                    <Label htmlFor="schedule" className="text-sm">Schedule for later</Label>
                  </div>
                  
                  {showScheduler && (
                    <div className="space-y-2">
                      <Popover>
                        <PopoverTrigger asChild>
                          <Button
                            variant="outline"
                            className="w-full justify-start text-left font-normal"
                          >
                            <Calendar className="mr-2 h-4 w-4" />
                            {scheduledDate ? format(scheduledDate, "PPP") : "Pick a date"}
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0">
                          <CalendarComponent
                            mode="single"
                            selected={scheduledDate}
                            onSelect={setScheduledDate}
                            initialFocus
                          />
                        </PopoverContent>
                      </Popover>
                      
                      <Button 
                        type="button" 
                        variant="secondary"
                        onClick={handleSchedule}
                        disabled={loading || !scheduledDate}
                        className="w-full"
                      >
                        <Calendar className="mr-2 h-4 w-4" />
                        Schedule Post
                      </Button>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Featured Image */}
            <div className="rounded-lg border bg-card p-6 space-y-4">
              <h3 className="font-semibold">Featured Image</h3>

              {previewUrl ? (
                <div className="relative group">
                  <div className="relative aspect-video rounded-md overflow-hidden border">
                    <img
                      src={previewUrl}
                      alt="Featured image preview"
                      className="w-full h-full object-cover"
                    />
                    <button
                      type="button"
                      onClick={() => {
                        setPreviewUrl(null)
                        form.setValue("featuredImage", "")
                      }}
                      className="absolute top-2 right-2 bg-red-500 text-white p-1.5 rounded-full hover:bg-red-600 transition-colors"
                    >
                      <X className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              ) : (
                <div className="border-2 border-dashed border-muted-foreground/25 rounded-md p-6 text-center hover:border-primary/50 transition-colors">
                  <UploadCloud className="h-10 w-10 text-muted-foreground mx-auto mb-2" />
                  <p className="text-sm text-muted-foreground mb-2">
                    Upload a featured image
                  </p>
                  <Input
                    type="file"
                    accept="image/*"
                    onChange={(e) => {
                      const file = e.target.files?.[0]
                      if (file) {
                        const url = URL.createObjectURL(file)
                        setPreviewUrl(url)
                        form.setValue("featuredImage", url)
                      }
                    }}
                    className="max-w-xs"
                  />
                </div>
              )}
            </div>

            {/* Category Selection */}
            <div className="rounded-lg border bg-card p-6 space-y-4">
              <h3 className="font-semibold">Category</h3>

              <FormField
                control={form.control}
                name="categoryId"
                render={({ field }) => (
                  <FormItem>
                    <Popover open={categoryOpen} onOpenChange={setCategoryOpen}>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant="outline"
                            role="combobox"
                            className={cn(
                              "w-full justify-between",
                              !field.value && "text-muted-foreground"
                            )}
                          >
                            {field.value
                              ? categories.find(cat => cat.id === field.value)?.name
                              : "Select category"}
                            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-full p-0">
                        <Command>
                          <CommandInput placeholder="Search categories..." />
                          <CommandEmpty>No categories found.</CommandEmpty>
                          <CommandGroup className="max-h-[200px] overflow-auto">
                            {categories.map((category) => (
                              <CommandItem
                                value={category.name}
                                key={category.id}
                                onSelect={() => {
                                  field.onChange(category.id)
                                  setCategoryOpen(false)
                                }}
                              >
                                <Check
                                  className={cn(
                                    "mr-2 h-4 w-4",
                                    category.id === field.value
                                      ? "opacity-100"
                                      : "opacity-0"
                                  )}
                                />
                                {category.name}
                              </CommandItem>
                            ))}
                          </CommandGroup>
                        </Command>
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Tags */}
            <div className="rounded-lg border bg-card p-6 space-y-4">
              <h3 className="font-semibold">Tags</h3>

              <div className="space-y-3">
                <div className="flex space-x-2">
                  <Input
                    placeholder="Add a tag..."
                    value={tagInput}
                    onChange={(e) => setTagInput(e.target.value)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        e.preventDefault()
                        const existingTag = tags.find(tag =>
                          tag.name.toLowerCase() === tagInput.toLowerCase()
                        )
                        if (existingTag) {
                          addTag(existingTag.id)
                        } else if (tagInput.trim()) {
                          // For now, just add the tag name - in a real app you'd create the tag
                          addTag(tagInput.trim())
                        }
                      }
                    }}
                  />
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => {
                      const existingTag = tags.find(tag =>
                        tag.name.toLowerCase() === tagInput.toLowerCase()
                      )
                      if (existingTag) {
                        addTag(existingTag.id)
                      } else if (tagInput.trim()) {
                        addTag(tagInput.trim())
                      }
                    }}
                  >
                    Add
                  </Button>
                </div>

                <div className="flex flex-wrap gap-2">
                  {selectedTags.map((tagId) => {
                    const tag = tags.find(t => t.id === tagId)
                    const tagName = tag?.name || tagId
                    return (
                      <Badge key={tagId} variant="secondary" className="flex items-center gap-1">
                        {tagName}
                        <button
                          type="button"
                          onClick={() => removeTag(tagId)}
                          className="ml-1 rounded-full hover:bg-muted"
                        >
                          <X className="h-3 w-3" />
                        </button>
                      </Badge>
                    )
                  })}
                </div>

                {tags.length > 0 && (
                  <div className="space-y-1">
                    <Label className="text-xs text-muted-foreground">Existing tags:</Label>
                    <div className="flex flex-wrap gap-1">
                      {tags.slice(0, 10).map((tag) => (
                        <button
                          key={tag.id}
                          type="button"
                          onClick={() => addTag(tag.id)}
                          className="text-xs px-2 py-1 bg-muted hover:bg-muted/80 rounded-md transition-colors"
                        >
                          {tag.name}
                        </button>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* SEO Settings */}
            <div className="rounded-lg border bg-card p-6 space-y-4">
              <h3 className="font-semibold">SEO Settings</h3>

              <FormField
                control={form.control}
                name="seoTitle"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Meta Title</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="SEO title for search engines"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      Recommended: 50-60 characters
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="seoDescription"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Meta Description</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="SEO description for search engines"
                        className="min-h-[80px] resize-none"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      Recommended: 150-160 characters
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Action Buttons */}
            <div className="flex space-x-2">
              {onCancel && (
                <Button
                  type="button"
                  variant="outline"
                  onClick={onCancel}
                  disabled={loading}
                  className="flex-1"
                >
                  Cancel
                </Button>
              )}
            </div>
          </div>
        </div>
      </form>
    </Form>
  )
}
