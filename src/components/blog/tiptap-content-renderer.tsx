'use client'

import React from 'react'

interface TipTapContentRendererProps {
  content: string
  className?: string
}

/**
 * TipTap content renderer with frontend design system styling
 * Uses the same typography and spacing patterns as other frontend pages
 */
export function TipTapContentRenderer({ content, className }: TipTapContentRendererProps) {
  if (!content || content.trim() === '') {
    return (
      <div className={`text-center py-4 ${className || ''}`}>
        <p className="text-muted">No content available.</p>
      </div>
    )
  }

  return (
    <div
      className={`blog-content-area ${className || ''}`}
      dangerouslySetInnerHTML={{ __html: content }}
    />
  )
}

/**
 * Fallback content renderer for when TipTap content is not available
 */
export function ContentFallback({ message = "No content available." }: { message?: string }) {
  return (
    <div className="text-center py-12 text-muted-foreground">
      <p>{message}</p>
    </div>
  )
}

/**
 * Content renderer that handles both regular content and blocks
 */
interface ContentRendererProps {
  content?: string
  blocks?: Array<{
    id: string
    type: string
    content: any
    order: number
  }>
  className?: string
}

export function ContentRenderer({ content, blocks, className }: ContentRendererProps) {
  // If we have blocks, render them instead of content
  if (blocks && blocks.length > 0) {
    return (
      <div className={cn("space-y-8", className)}>
        {blocks
          .sort((a, b) => a.order - b.order)
          .map((block) => (
            <BlockRenderer
              key={block.id}
              block={block}
            />
          ))}
      </div>
    )
  }

  // If we have content, render it with TipTap renderer
  if (content) {
    return <TipTapContentRenderer content={content} className={className} />
  }

  // Fallback when no content is available
  return <ContentFallback />
}
