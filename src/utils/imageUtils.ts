/**
 * Image utility functions for handling image paths and fallbacks
 */

// Default fallback images
export const DEFAULT_FALLBACK_IMAGES = {
  project: '/assets/img/project/default-project.jpg',
  placeholder: '/placeholder.svg',
  placeholderSquare: '/assets/img/placeholders/project-placeholder-square.svg',
  placeholderWide: '/assets/img/placeholders/project-placeholder.svg',
  logo: '/assets/img/logo/logo_icon.svg',
  user: '/placeholder-user.jpg',
  blog: '/assets/img/blog/blog2-img1.png'
};

// Image path prefixes
export const IMAGE_PATH_PREFIXES = {
  assets: '/assets/',
  mitc: '/assets/mitc_images/',
  img: '/assets/img/'
};

/**
 * Resolves image path to ensure it starts with /assets/ or is a valid external URL
 * @param path - The image path to resolve
 * @returns Resolved image path
 */
export function resolveImagePath(path: string): string {
  if (!path) return DEFAULT_FALLBACK_IMAGES.placeholder;

  // If path is a full URL (http/https), return as is
  if (path.startsWith('http://') || path.startsWith('https://')) {
    return path;
  }

  // If path is a data URL, return as is
  if (path.startsWith('data:')) {
    return path;
  }

  // If path already starts with /assets/, return as is
  if (path.startsWith('/assets/')) {
    return path;
  }

  // If path starts with /img/, convert to /assets/img/
  if (path.startsWith('/img/')) {
    return path.replace('/img/', '/assets/img/');
  }

  // If path starts with /mitc_images/, convert to /assets/mitc_images/
  if (path.startsWith('/mitc_images/')) {
    return path.replace('/mitc_images/', '/assets/mitc_images/');
  }

  // If path doesn't start with /, assume it's relative to assets
  if (!path.startsWith('/')) {
    return `/assets/${path}`;
  }

  return path;
}

/**
 * Gets appropriate fallback image based on context
 * @param context - The context where the image is used
 * @returns Fallback image path
 */
export function getFallbackImage(context: 'project' | 'blog' | 'user' | 'logo' | 'general' = 'general'): string {
  switch (context) {
    case 'project':
      return DEFAULT_FALLBACK_IMAGES.project;
    case 'blog':
      return DEFAULT_FALLBACK_IMAGES.blog;
    case 'user':
      return DEFAULT_FALLBACK_IMAGES.user;
    case 'logo':
      return DEFAULT_FALLBACK_IMAGES.logo;
    default:
      return DEFAULT_FALLBACK_IMAGES.placeholder;
  }
}

/**
 * Validates if an image path exists (client-side check)
 * @param path - The image path to validate
 * @returns Promise that resolves to true if image exists
 */
export function validateImagePath(path: string): Promise<boolean> {
  return new Promise((resolve) => {
    const img = new Image();
    img.onload = () => resolve(true);
    img.onerror = () => resolve(false);
    img.src = path;
  });
}

/**
 * Gets multiple fallback options for an image
 * @param originalPath - The original image path
 * @param context - The context where the image is used
 * @returns Array of fallback image paths
 */
export function getImageFallbacks(originalPath: string, context: 'project' | 'blog' | 'user' | 'logo' | 'general' = 'general'): string[] {
  const fallbacks = [resolveImagePath(originalPath)];
  
  // Add context-specific fallback
  const contextFallback = getFallbackImage(context);
  if (!fallbacks.includes(contextFallback)) {
    fallbacks.push(contextFallback);
  }
  
  // Add general fallbacks
  if (!fallbacks.includes(DEFAULT_FALLBACK_IMAGES.placeholderWide)) {
    fallbacks.push(DEFAULT_FALLBACK_IMAGES.placeholderWide);
  }
  
  if (!fallbacks.includes(DEFAULT_FALLBACK_IMAGES.placeholder)) {
    fallbacks.push(DEFAULT_FALLBACK_IMAGES.placeholder);
  }
  
  return fallbacks;
}

/**
 * Optimizes image path for different screen sizes
 * @param path - The original image path
 * @param size - The target size (small, medium, large)
 * @returns Optimized image path
 */
export function getOptimizedImagePath(path: string, size: 'small' | 'medium' | 'large' = 'medium'): string {
  const resolvedPath = resolveImagePath(path);
  
  // For now, return the resolved path as is
  // In the future, this could be enhanced to return different sized versions
  return resolvedPath;
}

/**
 * Extracts image metadata from path
 * @param path - The image path
 * @returns Image metadata object
 */
export function getImageMetadata(path: string) {
  const resolvedPath = resolveImagePath(path);
  const filename = resolvedPath.split('/').pop() || '';
  const extension = filename.split('.').pop()?.toLowerCase() || '';
  
  return {
    path: resolvedPath,
    filename,
    extension,
    isVector: ['svg'].includes(extension),
    isRaster: ['jpg', 'jpeg', 'png', 'gif', 'webp'].includes(extension),
    directory: resolvedPath.substring(0, resolvedPath.lastIndexOf('/'))
  };
}

/**
 * Converts old image paths to new standardized paths
 * @param oldPath - The old image path
 * @returns New standardized path
 */
export function migrateImagePath(oldPath: string): string {
  if (!oldPath) return DEFAULT_FALLBACK_IMAGES.placeholder;
  
  // Handle mitc_images paths
  if (oldPath.includes('mitc_images')) {
    return resolveImagePath(oldPath);
  }
  
  // Handle old /img/ paths
  if (oldPath.startsWith('/img/')) {
    return oldPath.replace('/img/', '/assets/img/');
  }
  
  // Handle relative paths
  if (!oldPath.startsWith('/') && !oldPath.startsWith('http')) {
    return `/assets/img/${oldPath}`;
  }
  
  return resolveImagePath(oldPath);
}
