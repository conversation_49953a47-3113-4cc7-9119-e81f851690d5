/*

Plugin Name: Multi Drop Down Mobile menu
version: 1.0
Author: <PERSON><PERSON>: 



*/




/*Base css*/

@media screen and (max-width:812px) {
    * {
        margin: 0;
        padding: 0;
    }

    body {
        font-size: 16px;
        font-family: 'arial', sans-serif;
    }

    a,
    a:hover {
        outline: none;
        text-decoration: none;
    }

    ul,
    li {
        list-style: none;
    }



    /*Menu Css*/

    .mobile-logo a {
        font-size: 20px;
        font-weight: 700;
        color: #000;
        text-decoration: none;
    }


    .mobile-logo-unick {
        margin-top: -30px;
        margin-bottom: 10px;
    }

    .mobile-header {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        padding: 17px 0;
        z-index: 999;
        background: #fff;
    }

    .mobile-header.mobile-header-main {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        padding: 17px 0;
        z-index: 999;
        background: #fff;
    }

    .mobile-header-elements {
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .mobile-nav-icon {
        font-size: 23px;
        border: 1px solid;
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 4px;
        color: #000;
    }

    .mobile-nav-icon {
        font-size: 23px;
        border: 1px solid;
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 4px;
        color: #000000;
    }

    .mobile-sidebar {
        position: fixed;
        height: 100%;
        width: 320px;
        max-width: 85vw;
        background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
        z-index: 9999;
        padding: 40px 30px;
        left: -100%;
        top: 0;
        visibility: hidden;
        opacity: 0;
        transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        box-shadow: 2px 0 20px rgba(0, 0, 0, 0.3);
    }

    /* Mobile Menu Overlay */
    .mobile-menu-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        z-index: 9998;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
        backdrop-filter: blur(2px);
    }

    .mobile-menu-overlay.active {
        opacity: 1;
        visibility: visible;
    }

    .mobile-sidebar .contact-icon {
        background: #fff;
    }


    .mobile-sidebar.mobile-menu-active {
        left: 0;
        visibility: visible;
        opacity: 1;
        transition: all .3s;
        padding-top: 55px;
        max-height: 100%;
        overflow-y: scroll;
    }

    /* Enhanced Mobile Menu Styles */
    .menu-item-with-toggle {
        position: relative;
    }

    .submenu-button {
        background: none;
        border: none;
        cursor: pointer;
        outline: none;
    }

    .submenu-button i {
        transition: transform 0.3s ease;
    }

    .sub-menu {
        padding-left: 0;
        margin-top: 8px;
        transition: all 0.3s ease;
    }

    .sub-menu li {
        margin-bottom: 4px;
    }

    .sub-menu li:last-child {
        margin-bottom: 0;
    }

    .menu-close {
        position: absolute;
        right: 20px;
        top: 20px;
        font-size: 25px;
        color: #000;
        cursor: pointer;
    }

    .mobile-nav li a {
        font-size: 18px;
        line-height: 24px;
        color: #000;
        padding: 10px 0;
        display: block;
    }


    ul.mobile-nav-list {
        margin-top: 30px;
    }


    li.has-children {
        position: relative;
        z-index: 2;
    }

    span.mobile-nav-menu-icon {
        position: absolute;
        right: 0;
        top: 0;
        height: 50px;
        width: 50px;
        text-align: center;
        line-height: 50px;
        color: #fff;
    }

    ul.mobile-menu-sub {
        display: none;
        position: relative;
        left: 0;
        padding-left: 10px;
        transition: all .3s;
    }

    ul.mobile-menu-sub.sub-menu-active {
        display: block;
        position: relative;
        transition: all .3s;
    }



    .mobile-nav li {
        position: relative;
        z-index: 2;
    }

    span.submenu-button {
        position: absolute;
        width: 100%;
        height: 44px;
        top: 0;
        left: 0;
        z-index: 3;
        transition: all .3s;
    }

    span.submenu-button:after,
    span.submenu-button:before {
        position: absolute;
        content: "";
        height: 20px;
        width: 2px;
        background: #000;
        top: 12px;
        right: 9px;
        transition: all .3s;
    }

    span.submenu-button:before {
        height: 2px;
        width: 20px;
        right: 0px;
        top: 22px;
    }

    span.submenu-button.submenu-opened:after {
        visibility: hidden;
        opacity: 0;
    }

    .sub-menu {
        display: none;
        padding-left: 15px;
    }


    span.multi-drop-icon {
        position: absolute;
        right: 10px;
        width: 20px;
        height: 100%;
        top: 0;
        text-align: center;
        display: flex;
        align-items: center;
        justify-content: flex-end;
    }


    ul.mobile-nav-list {
        max-height: 500px;
        overflow-y: scroll;
    }

    .single-footer.single-footer-menu.single-footer4 h3 {
        margin-bottom: 0;
    }

    .social.social4-menu.social4 {
        margin-top: 0;
    }

    a.mobile-menu-button.hash-nav {
        /* background: blue; */
        padding: 15px;
        border-radius: 5px;
        margin-top: 20px;
        border: 1px solid;
        text-align: center;
        margin-bottom: 20px;
    }

}

.mobile-menu-a a {
    color: #fff;
    font-size: 16px;
    line-height: 16px;
}

.mobile-menu-icon-box {
    display: flex;
    align-items: start;
    padding: 5px 0;
}

.mobile-menu-icon {
    padding-right: 8px;
}

.mobile-menu-social h3 {
    font-size: 20px;
    line-height: 24px;
    font-weight: 600;
    color: #fff;
    padding: 20px 0;
}