/**
 * CSS styles for Image Mask Components
 * Provides styling for image masking effects, animations, and loading states
 */

/* Base container styles */
.image-mask-container,
.multi-image-mask-container {
  position: relative;
  display: inline-block;
  overflow: hidden;
}

/* Base image styles */
.image-mask,
.multi-image-mask {
  display: block;
  width: 100%;
  height: auto;
  object-fit: cover;
  transition: all 0.3s ease-in-out;
}

/* Loading states */
.image-mask--loading,
.multi-image-mask--loading {
  opacity: 0;
}

.image-mask-loading,
.multi-image-mask-loading {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #e9ecef;
  border-top: 3px solid #6c757d;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Error states */
.image-mask--error,
.multi-image-mask--error {
  background-color: #f8f9fa;
  border: 2px dashed #dee2e6;
}

/* Masked states */
.image-mask--masked,
.multi-image-mask--masked {
  opacity: 1;
}

/* Animation states */
.image-mask--animated,
.multi-image-mask--animated {
  animation-play-state: running;
}

/* Mask-specific animations */
@keyframes maskFadeIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes maskPulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes maskRotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes maskBounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -10px, 0);
  }
  70% {
    transform: translate3d(0, -5px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}

/* Preset animation classes */
.mask-animation-fadeIn {
  animation: maskFadeIn 1s ease-in-out forwards;
}

.mask-animation-pulse {
  animation: maskPulse 2s ease-in-out infinite alternate;
}

.mask-animation-rotate {
  animation: maskRotate 3s linear infinite;
}

.mask-animation-bounce {
  animation: maskBounce 1.5s cubic-bezier(0.68, -0.55, 0.265, 1.55) infinite alternate;
}

/* Responsive design */
@media (max-width: 768px) {
  .image-mask-container,
  .multi-image-mask-container {
    max-width: 100%;
  }
  
  .loading-spinner {
    width: 30px;
    height: 30px;
    border-width: 2px;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .image-mask--error,
  .multi-image-mask--error {
    border-color: #000;
    background-color: #fff;
  }
  
  .loading-spinner {
    border-color: #000;
    border-top-color: #666;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .image-mask,
  .multi-image-mask,
  .loading-spinner {
    animation: none !important;
    transition: none !important;
  }
  
  .image-mask--animated,
  .multi-image-mask--animated {
    animation: none !important;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .image-mask-loading,
  .multi-image-mask-loading {
    background-color: #1a1a1a;
  }
  
  .image-mask--error,
  .multi-image-mask--error {
    background-color: #1a1a1a;
    border-color: #333;
  }
  
  .loading-spinner {
    border-color: #333;
    border-top-color: #999;
  }
}

/* Focus states for accessibility */
.image-mask:focus,
.multi-image-mask:focus {
  outline: 2px solid #007bff;
  outline-offset: 2px;
}

/* Print styles */
@media print {
  .image-mask,
  .multi-image-mask {
    -webkit-mask: none !important;
    mask: none !important;
    animation: none !important;
  }
  
  .image-mask-loading,
  .multi-image-mask-loading {
    display: none;
  }
}

/* Utility classes for common mask effects */
.mask-circle {
  -webkit-mask-image: radial-gradient(circle, black 50%, transparent 50%);
  mask-image: radial-gradient(circle, black 50%, transparent 50%);
}

.mask-fade-right {
  -webkit-mask-image: linear-gradient(to right, black, transparent);
  mask-image: linear-gradient(to right, black, transparent);
}

.mask-fade-bottom {
  -webkit-mask-image: linear-gradient(to bottom, black, transparent);
  mask-image: linear-gradient(to bottom, black, transparent);
}

.mask-triangle {
  -webkit-mask-image: polygon(50% 0%, 0% 100%, 100% 100%);
  mask-image: polygon(50% 0%, 0% 100%, 100% 100%);
}

.mask-diamond {
  -webkit-mask-image: polygon(50% 0%, 100% 50%, 50% 100%, 0% 50%);
  mask-image: polygon(50% 0%, 100% 50%, 50% 100%, 0% 50%);
}

.mask-hexagon {
  -webkit-mask-image: polygon(30% 0%, 70% 0%, 100% 50%, 70% 100%, 30% 100%, 0% 50%);
  mask-image: polygon(30% 0%, 70% 0%, 100% 50%, 70% 100%, 30% 100%, 0% 50%);
}

/* Common mask properties */
.mask-cover {
  -webkit-mask-size: cover;
  mask-size: cover;
}

.mask-contain {
  -webkit-mask-size: contain;
  mask-size: contain;
}

.mask-center {
  -webkit-mask-position: center;
  mask-position: center;
}

.mask-no-repeat {
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
}

/* Integration with existing styles */
.icon-mask-effect {
  transition: all 0.3s ease-in-out;
}

.icon-mask-effect:hover {
  transform: scale(1.1);
}

/* Enhanced image positioning for Section4 */
.images-all .image1 .image-mask-container,
.images-all .image2 .multi-image-mask-container {
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.images-all .image2 {
  position: relative;
  z-index: 2;
}

.images-all .image1 {
  position: relative;
  z-index: 1;
}

/* Responsive adjustments */
@media (max-width: 991px) {
  .images-all .image2 {
    transform: none !important;
    margin-top: 2rem;
  }

  .icon-mask-effect {
    width: 50px !important;
    height: 50px !important;
  }
}

@media (max-width: 576px) {
  .images-all .image1 .image-mask,
  .images-all .image2 .multi-image-mask {
    max-height: 250px !important;
  }
}
